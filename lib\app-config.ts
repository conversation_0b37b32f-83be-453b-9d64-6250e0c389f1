export const APP_CONFIG = {
  appStore: {
    url: "https://apps.apple.com/us/app/zimbora/id6743491776",
    badge: "/icons/app-store-badge.png",
    alt: "Download on the App Store",
  },

  googlePlay: {
    url: "https://play.google.com/store/apps/details?id=ao.zimbora",
    badge: "/icons/google-play-badge.png",
    alt: "Get it on Google Play",
  },

  deepLink: {
    scheme: "zimbora://",
    packageName: "ao.zimbora",
  },

  app: {
    name: "<PERSON>imbor<PERSON>",
    description: "A melhor plataforma de eventos de Luanda",
  },
} as const;

export const generateDeepLink = (path: string): string => {
  return `${APP_CONFIG.deepLink.scheme}${path}`;
};

export const getDownloadOptions = () => [
  APP_CONFIG.googlePlay,
  APP_CONFIG.appStore,
];
